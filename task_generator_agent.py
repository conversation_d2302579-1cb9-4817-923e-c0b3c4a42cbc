"""
Task Generator Agent - <PERSON>je fiki<PERSON> detaylı task listesine dönüştürür
"""
from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, RunYield, RunYieldResume, Server
from config import OPENROUTER_API_KEY
import json
import logging
import openai

logger = logging.getLogger(__name__)

# OpenAI client for OpenRouter
client = openai.OpenAI(
    api_key=OPENROUTER_API_KEY,
    base_url="https://openrouter.ai/api/v1"
)

# OpenRouter configuration loaded from config.py

server = Server()

@server.agent()
async def task_generator(input: list[Message], context: Context) -> AsyncGenerator[RunYield, RunYieldResume]:
    """
    Proje fikirlerini detaylı task listesine dönüştüren agent.

    Input: Proje fikri açıklaması
    Output: JSON formatında detaylı task listesi
    """

    project_idea = input[0].parts[0].content

    system_prompt = """Sen deneyimli bir proje yöneticisi ve task planlayıcısısın.
    Proje fikirlerini alıp bunları detaylı, uygulanabilir task'lara bölebilirsin.
    Her task için açık tanım, tahmini süre, öncelik seviyesi, gerekli beceriler ve bağımlılıklar belirlersin.
    Sonucu JSON formatında döndürmelisin."""

    task_prompt = f"""
    Proje Fikri: {project_idea}

    Bu proje fikrini detaylı task listesine dönüştür. Her task için şu bilgileri içer:

    1. Task ID (T001, T002, vb.)
    2. Task Adı
    3. Detaylı Açıklama
    4. Tahmini Süre (saat)
    5. Öncelik (High/Medium/Low)
    6. Gerekli Beceriler
    7. Bağımlılıklar (diğer task ID'leri)
    8. Kategori (Frontend, Backend, Database, Testing, vb.)

    Sonucu şu JSON formatında döndür:
    {{
        "project_name": "Proje Adı",
        "total_estimated_hours": 0,
        "tasks": [
            {{
                "id": "T001",
                "name": "Task Adı",
                "description": "Detaylı açıklama",
                "estimated_hours": 8,
                "priority": "High",
                "skills": ["Python", "React"],
                "dependencies": [],
                "category": "Backend"
            }}
        ]
    }}
    """

    try:
        # LiteLLM ile doğrudan çağrı yap
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": task_prompt}
        ]

        response = client.chat.completions.create(
            model="meta-llama/llama-3.3-70b-instruct:free",
            messages=messages,
            max_tokens=4096,
            temperature=0.7
        )

        result = response.choices[0].message.content
        logger.info("Task generation completed successfully")
        logger.info(f"Generated tasks: {result}")

        # JSON formatını kontrol et
        try:
            json.loads(result)
            response_content = result
        except json.JSONDecodeError:
            # JSON formatı bozuksa düzelt
            response_content = f'{{"error": "JSON format hatası", "raw_output": "{result}"}}'

        yield Message(parts=[MessagePart(content=response_content)])

    except Exception as e:
        logger.error(f"Task generation failed: {e}")
        error_response = f'{{"error": "Task generation failed: {str(e)}"}}'
        yield Message(parts=[MessagePart(content=error_response)])

if __name__ == "__main__":
    server.run(port=8002)
