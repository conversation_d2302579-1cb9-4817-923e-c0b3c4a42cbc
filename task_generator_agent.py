"""
Task Generator Agent - <PERSON><PERSON> fiki<PERSON> detaylı task listesine dö<PERSON>üştürür
"""
from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, RunYield, RunYieldResume, Server
from crewai import Crew, Task, Agent, LLM
from config import get_crewai_llm_config
import json
import logging

logger = logging.getLogger(__name__)

# OpenRouter LLM configuration
llm_config = get_crewai_llm_config()
llm = LLM(
    model=llm_config["model"],
    base_url=llm_config["base_url"],
    api_key=llm_config["api_key"],
    max_tokens=llm_config["max_tokens"],
    temperature=llm_config["temperature"]
)

# Task Generator Agent
task_generator_agent = Agent(
    role="Senior Project Task Generator",
    goal="Proje fikirlerini detaylı, uygulanabilir task listelerine dönüştürmek",
    backstory="""Sen deneyimli bir proje yöneticisi ve task planlayıcısısın. 
    Proje fikirlerini alıp bunları detaylı, uygulanabilir task'lara bölebilirsin.
    Her task için:
    - Açık ve net tanım
    - Tahmini süre (saat cinsinden)
    - Öncelik seviyesi (High, Medium, Low)
    - Gerekli beceriler
    - Bağımlılıklar
    
    Sonucu JSON formatında döndürmelisin.""",
    verbose=True,
    allow_delegation=False,
    llm=llm,
    max_retry_limit=3
)

server = Server()

@server.agent()
async def task_generator(input: list[Message], context: Context) -> AsyncGenerator[RunYield, RunYieldResume]:
    """
    Proje fikirlerini detaylı task listesine dönüştüren agent.
    
    Input: Proje fikri açıklaması
    Output: JSON formatında detaylı task listesi
    """
    
    project_idea = input[0].parts[0].content
    
    task_prompt = f"""
    Proje Fikri: {project_idea}
    
    Bu proje fikrini detaylı task listesine dönüştür. Her task için şu bilgileri içer:
    
    1. Task ID (T001, T002, vb.)
    2. Task Adı
    3. Detaylı Açıklama
    4. Tahmini Süre (saat)
    5. Öncelik (High/Medium/Low)
    6. Gerekli Beceriler
    7. Bağımlılıklar (diğer task ID'leri)
    8. Kategori (Frontend, Backend, Database, Testing, vb.)
    
    Sonucu şu JSON formatında döndür:
    {{
        "project_name": "Proje Adı",
        "total_estimated_hours": 0,
        "tasks": [
            {{
                "id": "T001",
                "name": "Task Adı",
                "description": "Detaylı açıklama",
                "estimated_hours": 8,
                "priority": "High",
                "skills": ["Python", "React"],
                "dependencies": [],
                "category": "Backend"
            }}
        ]
    }}
    """
    
    task = Task(
        description=task_prompt,
        expected_output="JSON formatında detaylı task listesi",
        agent=task_generator_agent
    )
    
    crew = Crew(
        agents=[task_generator_agent],
        tasks=[task],
        verbose=True
    )
    
    try:
        task_output = await crew.kickoff_async()
        logger.info("Task generation completed successfully")
        logger.info(f"Generated tasks: {task_output}")
        
        # JSON formatını kontrol et ve düzelt
        result = str(task_output)
        
        # JSON'u parse etmeye çalış
        try:
            json.loads(result)
            response = result
        except json.JSONDecodeError:
            # JSON formatı bozuksa düzelt
            response = f'{{"error": "JSON format hatası", "raw_output": "{result}"}}'
            
        yield Message(parts=[MessagePart(content=response)])
        
    except Exception as e:
        logger.error(f"Task generation failed: {e}")
        error_response = f'{{"error": "Task generation failed: {str(e)}"}}'
        yield Message(parts=[MessagePart(content=error_response)])

if __name__ == "__main__":
    server.run(port=8002)
