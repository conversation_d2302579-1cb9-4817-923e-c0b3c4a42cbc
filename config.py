"""
OpenRouter LLM Configuration
"""
import os
from smolagents import LiteLLMModel
from dotenv import load_dotenv
load_dotenv()

# OpenRouter API configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# Default model for agents
DEFAULT_MODEL = "meta-llama/llama-3.3-70b-instruct:free"

def get_openrouter_model(model_name: str = DEFAULT_MODEL, max_tokens: int = 4096):
    """
    Create LiteLLM model configured for OpenRouter
    
    Args:
        model_name: OpenRouter model name (e.g., "meta-llama/llama-3.3-70b-instruct:free")
        max_tokens: Maximum tokens for response
    
    Returns:
        LiteLLMModel: Configured model instance
    """
    return LiteLLMModel(
        model_id=model_name,
        api_base=OPENROUTER_BASE_URL,
        api_key=OPENROUTER_API_KEY,
        max_tokens=max_tokens,
        temperature=0.7
    )

# CrewAI LLM configuration for OpenRouter
def get_crewai_llm_config(model_name: str = DEFAULT_MODEL):
    """
    Get CrewAI LLM configuration for OpenRouter
    
    Args:
        model_name: OpenRouter model name
        
    Returns:
        dict: CrewAI LLM configuration
    """
    return {
        "model": f"openrouter/{model_name}",
        "base_url": OPENROUTER_BASE_URL,
        "api_key": OPENROUTER_API_KEY,
        "max_tokens": 4096,
        "temperature": 0.7
    }
