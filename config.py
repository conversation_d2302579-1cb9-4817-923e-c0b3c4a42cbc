"""
OpenRouter LLM Configuration
"""
import os
from smolagents import LiteLLMModel
from dotenv import load_dotenv
load_dotenv()

# OpenRouter API configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# API key kontrolü
if not OPENROUTER_API_KEY:
    print("⚠️  OPENROUTER_API_KEY bulunamadı! .env dosyasını kontrol edin.")
    print("   .env dosyasında: OPENROUTER_API_KEY=your-api-key")

# Default model for agents
DEFAULT_MODEL = "openrouter/qwen/qwen-2.5-72b-instruct:free"

def get_openrouter_model(model_name: str = DEFAULT_MODEL, max_tokens: int = 4096):
    """
    Create LiteLLM model configured for OpenRouter

    Args:
        model_name: OpenRouter model name (e.g., "openrouter/meta-llama/llama-3.3-70b-instruct:free")
        max_tokens: Maximum tokens for response

    Returns:
        LiteLLMModel: Configured model instance
    """
    return LiteLLMModel(
        model_id=model_name,
        api_base=OPENROUTER_BASE_URL,
        api_key=OPENROUTER_API_KEY,
        max_tokens=max_tokens,
        temperature=0.7
    )

# CrewAI LLM configuration for OpenRouter
def get_crewai_llm_config(model_name: str = "qwen/qwen-2.5-72b-instruct:free"):
    """
    Get CrewAI LLM configuration for OpenRouter

    Args:
        model_name: OpenRouter model name (without openrouter/ prefix)

    Returns:
        dict: CrewAI LLM configuration
    """
    return {
        "model": f"openrouter/{model_name}",
        "base_url": OPENROUTER_BASE_URL,
        "api_key": OPENROUTER_API_KEY,
        "max_tokens": 4096,
        "temperature": 0.7
    }
