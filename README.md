# Proje <PERSON>

ACP (Agent Communication Protocol) tabanlı proje yönetim sistemi. <PERSON>je fi<PERSON> alıp detaylı task listesi ve zaman çizelgesi oluşturur.

## Sistem Bileşenleri

### 🤖 Agent'lar
- **Task Generator Agent** (Port 8002): <PERSON><PERSON> fi<PERSON> detaylı task listesine dönüştürür
- **Scheduler Agent** (Port 8003): Task'ları alıp optimal zaman çizelgesi oluşturur

### 💻 Client
- **Project Manager Client**: <PERSON><PERSON><PERSON><PERSON><PERSON>dan proje fikri alıp agent'larla iletişim kurar

## Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
pip install -r requirements.txt
```

### 2. OpenRouter API Key
OpenRouter API key'inizi environment variable olarak ayarlayın:

```bash
# Windows
set OPENROUTER_API_KEY=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

# Linux/Mac
export OPENROUTER_API_KEY=your-api-key-here
```

### 3. <PERSON>ste<PERSON>lat<PERSON>

#### Otomatik Başlatma (Önerilen)
```bash
python start_system.py
```

#### Manuel Başlatma
Farklı terminal'lerde:

```bash
# Terminal 1 - Task Generator Agent
python task_generator_agent.py

# Terminal 2 - Scheduler Agent  
python scheduler_agent.py

# Terminal 3 - Client
python project_manager_client.py
```

## Kullanım

1. Sistem başlatıldıktan sonra proje fikrinizi detaylı olarak açıklayın
2. Sistem otomatik olarak:
   - Detaylı task listesi oluşturacak
   - Optimal zaman çizelgesi hazırlayacak
3. Sonuçları terminal'de görüntüleyebilirsiniz

### Örnek Proje Fikri
```
E-ticaret web sitesi geliştirmek istiyorum. 
Kullanıcı kayıt/giriş sistemi, ürün kataloğu, 
sepet yönetimi ve ödeme sistemi olacak.
React frontend ve Node.js backend kullanacağım.
```

## Özellikler

### Task Generator
- ✅ Proje fikirlerini detaylı task'lara böler
- ✅ Her task için süre tahmini
- ✅ Öncelik seviyesi belirleme
- ✅ Gerekli beceriler listesi
- ✅ Task bağımlılıkları
- ✅ Kategori sınıflandırması

### Scheduler
- ✅ Task bağımlılıklarını dikkate alır
- ✅ Öncelik seviyelerine göre sıralama
- ✅ Paralel çalışma imkanları
- ✅ Günlük 8 saatlik çalışma planı
- ✅ Hafta sonu tatil hesaplaması
- ✅ Milestone'lar

## Teknik Detaylar

### Kullanılan Teknolojiler
- **ACP SDK**: Agent'lar arası iletişim
- **CrewAI**: Agent framework
- **OpenRouter**: LLM API
- **LiteLLM**: Model abstraction
- **Colorama**: Terminal renklendirme

### Port'lar
- 8002: Task Generator Agent
- 8003: Scheduler Agent

### Desteklenen Modeller
- anthropic/claude-3.5-sonnet (varsayılan)
- Diğer OpenRouter modelleri config.py'dan değiştirilebilir

## Sorun Giderme

### Agent Başlatma Sorunları
- Port'ların kullanımda olmadığından emin olun
- OpenRouter API key'in doğru ayarlandığını kontrol edin
- Internet bağlantınızı kontrol edin

### JSON Format Hataları
- Agent'lar bazen JSON formatında olmayan çıktı verebilir
- Sistem otomatik olarak hata mesajı gösterir
- Farklı bir proje fikri ile tekrar deneyin

## Geliştirme

### Yeni Agent Ekleme
1. `config.py`'dan LLM konfigürasyonu alın
2. ACP server pattern'ini kullanın
3. `start_system.py`'a yeni agent'ı ekleyin

### Model Değiştirme
`config.py` dosyasında `DEFAULT_MODEL` değişkenini güncelleyin.

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
