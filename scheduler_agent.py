"""
Scheduler Agent - Task'ları alıp zaman çizelgesi oluşturur
"""
from collections.abc import AsyncGenerator
from acp_sdk.models import Message, MessagePart
from acp_sdk.server import Context, RunYield, RunYieldResume, Server
from crewai import Crew, Task, Agent, LLM
from config import get_crewai_llm_config
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# OpenRouter LLM configuration
llm_config = get_crewai_llm_config()
llm = LLM(
    model=llm_config["model"],
    base_url=llm_config["base_url"],
    api_key=llm_config["api_key"],
    max_tokens=llm_config["max_tokens"],
    temperature=llm_config["temperature"]
)

# Scheduler Agent
scheduler_agent = Agent(
    role="Senior Project Scheduler",
    goal="Task listelerini alıp optimal zaman çizelgesi oluşturmak",
    backstory="""Sen deneyimli bir proje planlayıcısı ve zaman yönetimi uzmanısın.
    Task listelerini alıp bunları optimal bir zaman çizelgesine dönüştürebilirsin.
    
    Şu faktörleri göz önünde bulundurursun:
    - Task bağımlılıkları
    - Öncelik seviyeleri
    - Tahmini süreler
    - Kaynak optimizasyonu
    - Paralel çalışma imkanları
    
    Sonucu JSON formatında döndürmelisin.""",
    verbose=True,
    allow_delegation=False,
    llm=llm,
    max_retry_limit=3
)

server = Server()

@server.agent()
async def scheduler(input: list[Message], context: Context) -> AsyncGenerator[RunYield, RunYieldResume]:
    """
    Task listesini alıp zaman çizelgesi oluşturan agent.
    
    Input: JSON formatında task listesi
    Output: JSON formatında zaman çizelgesi
    """
    
    tasks_json = input[0].parts[0].content
    
    # Bugünün tarihini al
    today = datetime.now().strftime("%Y-%m-%d")
    
    schedule_prompt = f"""
    Task Listesi: {tasks_json}
    
    Bu task listesini optimal bir zaman çizelgesine dönüştür. Başlangıç tarihi: {today}
    
    Şu faktörleri göz önünde bulundur:
    1. Task bağımlılıkları - bağımlı task'lar önce tamamlanmalı
    2. Öncelik seviyeleri - High öncelikli task'lar önce
    3. Paralel çalışma - bağımsız task'lar paralel yapılabilir
    4. Günlük 8 saatlik çalışma
    5. Hafta sonu tatil (Cumartesi-Pazar)
    
    Her task için şu bilgileri içer:
    - Başlangıç tarihi ve saati
    - Bitiş tarihi ve saati
    - Süre (saat)
    - Paralel çalışılabilecek diğer task'lar
    
    Sonucu şu JSON formatında döndür:
    {{
        "project_name": "Proje Adı",
        "schedule_created": "{today}",
        "total_duration_days": 0,
        "working_hours_per_day": 8,
        "schedule": [
            {{
                "task_id": "T001",
                "task_name": "Task Adı",
                "start_date": "2024-01-15",
                "start_time": "09:00",
                "end_date": "2024-01-15",
                "end_time": "17:00",
                "duration_hours": 8,
                "day_of_week": "Monday",
                "parallel_tasks": ["T002"],
                "status": "scheduled"
            }}
        ],
        "milestones": [
            {{
                "name": "Milestone Adı",
                "date": "2024-01-20",
                "completed_tasks": ["T001", "T002"]
            }}
        ]
    }}
    """
    
    task = Task(
        description=schedule_prompt,
        expected_output="JSON formatında detaylı zaman çizelgesi",
        agent=scheduler_agent
    )
    
    crew = Crew(
        agents=[scheduler_agent],
        tasks=[task],
        verbose=True
    )
    
    try:
        schedule_output = await crew.kickoff_async()
        logger.info("Schedule generation completed successfully")
        logger.info(f"Generated schedule: {schedule_output}")
        
        # JSON formatını kontrol et ve düzelt
        result = str(schedule_output)
        
        # JSON'u parse etmeye çalış
        try:
            json.loads(result)
            response = result
        except json.JSONDecodeError:
            # JSON formatı bozuksa düzelt
            response = f'{{"error": "JSON format hatası", "raw_output": "{result}"}}'
            
        yield Message(parts=[MessagePart(content=response)])
        
    except Exception as e:
        logger.error(f"Schedule generation failed: {e}")
        error_response = f'{{"error": "Schedule generation failed: {str(e)}"}}'
        yield Message(parts=[MessagePart(content=error_response)])

if __name__ == "__main__":
    server.run(port=8003)
