"""
Proje <PERSON>im Sistemi <PERSON>latıcı
Tüm agent'ları ve client'ı başlatır
"""
import asyncio
import subprocess
import sys
import time
import os
from colorama import Fore, Style, init

# Colorama'yı başlat
init(autoreset=True)

class SystemStarter:
    def __init__(self):
        self.processes = []
        
    def start_agent(self, script_name, port, agent_name):
        """Bir agent'ı başlat"""
        try:
            print(f"{Fore.CYAN}🚀 {agent_name} başlatılıyor (Port: {port})...")
            
            # Python script'ini başlat
            process = subprocess.Popen(
                [sys.executable, script_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes.append({
                'process': process,
                'name': agent_name,
                'port': port,
                'script': script_name
            })
            
            print(f"{Fore.GREEN}✅ {agent_name} başlatıldı (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ {agent_name} başlatılamadı: {e}")
            return False
    
    def check_agents_health(self):
        """Agent'ların durumunu kontrol et"""
        print(f"\n{Fore.CYAN}🔍 Agent durumları kontrol ediliyor...")
        
        healthy_agents = 0
        for agent_info in self.processes:
            process = agent_info['process']
            name = agent_info['name']
            
            if process.poll() is None:  # Process hala çalışıyor
                print(f"{Fore.GREEN}✅ {name}: Çalışıyor")
                healthy_agents += 1
            else:
                print(f"{Fore.RED}❌ {name}: Durdu")
                # Hata çıktısını göster
                stderr = process.stderr.read()
                if stderr:
                    print(f"{Fore.RED}   Hata: {stderr}")
        
        return healthy_agents == len(self.processes)
    
    def stop_all_agents(self):
        """Tüm agent'ları durdur"""
        print(f"\n{Fore.YELLOW}🛑 Tüm agent'lar durduruluyor...")
        
        for agent_info in self.processes:
            process = agent_info['process']
            name = agent_info['name']
            
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"{Fore.YELLOW}🛑 {name} durduruldu")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"{Fore.RED}💀 {name} zorla kapatıldı")
            except Exception as e:
                print(f"{Fore.RED}❌ {name} kapatılırken hata: {e}")
    
    def display_system_info(self):
        """Sistem bilgilerini göster"""
        print(f"\n{Fore.MAGENTA}{'='*60}")
        print(f"{Fore.MAGENTA}🎯 PROJE YÖNETİM SİSTEMİ")
        print(f"{Fore.MAGENTA}{'='*60}")
        print(f"{Fore.WHITE}Bu sistem şu bileşenlerden oluşur:")
        print(f"{Fore.CYAN}📋 Task Generator Agent (Port 8002)")
        print(f"{Fore.CYAN}📅 Scheduler Agent (Port 8003)")
        print(f"{Fore.CYAN}💻 Project Manager Client")
        print(f"{Fore.MAGENTA}{'-'*60}")
        print(f"{Fore.YELLOW}⚠️  OpenRouter API Key gereklidir!")
        print(f"{Fore.YELLOW}   OPENROUTER_API_KEY environment variable'ını ayarlayın")
        print(f"{Fore.MAGENTA}{'='*60}")
    
    async def start_system(self):
        """Sistemi başlat"""
        self.display_system_info()
        
        # Environment variable kontrolü
        if not os.getenv("OPENROUTER_API_KEY"):
            print(f"{Fore.RED}❌ OPENROUTER_API_KEY environment variable bulunamadı!")
            print(f"{Fore.YELLOW}Lütfen OpenRouter API key'inizi ayarlayın:")
            print(f"{Fore.WHITE}export OPENROUTER_API_KEY='your-api-key'")
            return False
        
        print(f"\n{Fore.CYAN}🚀 Sistem başlatılıyor...")
        
        # Agent'ları başlat
        agents = [
            ("task_generator_agent.py", 8002, "Task Generator Agent"),
            ("scheduler_agent.py", 8003, "Scheduler Agent")
        ]
        
        success_count = 0
        for script, port, name in agents:
            if self.start_agent(script, port, name):
                success_count += 1
            time.sleep(2)  # Agent'lar arası bekleme
        
        if success_count != len(agents):
            print(f"{Fore.RED}❌ Bazı agent'lar başlatılamadı!")
            self.stop_all_agents()
            return False
        
        # Agent'ların hazır olmasını bekle
        print(f"\n{Fore.CYAN}⏳ Agent'ların hazır olması bekleniyor...")
        time.sleep(5)
        
        # Sağlık kontrolü
        if not self.check_agents_health():
            print(f"{Fore.RED}❌ Bazı agent'lar sağlıklı değil!")
            self.stop_all_agents()
            return False
        
        print(f"\n{Fore.GREEN}✅ Tüm agent'lar hazır!")
        print(f"{Fore.CYAN}💻 Client başlatılıyor...")
        
        try:
            # Client'ı başlat
            from project_manager_client import main
            await main()
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Sistem kapatılıyor...")
        except Exception as e:
            print(f"{Fore.RED}❌ Client hatası: {e}")
        finally:
            self.stop_all_agents()
        
        return True

async def main():
    """Ana fonksiyon"""
    starter = SystemStarter()
    
    try:
        await starter.start_system()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Sistem kapatılıyor...")
        starter.stop_all_agents()
    except Exception as e:
        print(f"{Fore.RED}❌ Sistem hatası: {e}")
        starter.stop_all_agents()

if __name__ == "__main__":
    asyncio.run(main())
